import config from '@payload-config';
import { getPayload } from 'payload';
import { literalTranslateToEnglish } from '@/lib/integrations/openai/client';
import { translateContent } from '@/lib/integrations/openai/responses-api-client';
import { englishOnlyContentEnhancement } from '@/lib/integrations/openai/unified-enhancement';
import { ContentProcessorIntegration } from '@/lib/server/content-processor-integration';
import type { CreateCandidateArticleData } from '@/lib/types';
import { cleanTitle } from '@/lib/utils/character-cleaning';
import { markdownToLexical } from '@/lib/utils/lexical';
import { createEmptyLexicalState } from '@/lib/utils/lexical-validation';
import {
  cleanEnglishTitle,
  cleanSourceTitle,
  validateTitleQuality,
} from '@/lib/utils/title-optimization';

/**
 * Create a candidate article from RSS processing
 * Updated to use English-only enhancement system with new article structure
 */
export async function createCandidateArticle(
  data: CreateCandidateArticleData,
  feedProcessingOptions?: {
    skipEnhancement?: boolean;
    skipTranslation?: boolean;
    maxArticlesPerRun?: number;
    customTimeout?: number;
    enableStealth?: boolean;
    feedLanguage?: 'en' | 'de'; // Use feed's language setting
  }
): Promise<{ id: string }> {
  try {
    const payload = await getPayload({ config });

    // Generate temporary title and slug (will be updated after enhancement)
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    const tempTitle = `pending-english-enhancement-${timestamp}-${random}`;
    const tempSlug = `pending-english-enhancement-${timestamp}-${random}`;

    // Use feed's language setting instead of trying to detect it from content
    const feedLanguage = feedProcessingOptions?.feedLanguage || 'de'; // Default to German
    const isEnglishFeed = feedLanguage === 'en';

    console.log(
      `🌐 Feed language: ${feedLanguage.toUpperCase()} (${isEnglishFeed ? 'English' : 'German'} feed)`
    );

    // Check if enhancement should be skipped based on feed settings
    const shouldSkipEnhancement =
      feedProcessingOptions?.skipEnhancement || false;
    const shouldSkipTranslation =
      feedProcessingOptions?.skipTranslation || false;

    if (shouldSkipEnhancement) {
      console.log(
        '⏭️ Skipping AI enhancement due to feed settings - processing as minimal content'
      );
    } else if (shouldSkipTranslation && !isEnglishFeed) {
      console.log(
        '⏭️ Skipping translation due to feed settings - keeping original German content'
      );
    } else {
      console.log(
        `🚀 Starting ${isEnglishFeed ? 'English content processing' : 'English-only content enhancement'} for: ${data.title}`
      );
    }

    // Check content size before attempting enhancement to prevent OpenAI API limits
    const contentLength = data.content.length;
    const maxContentSize = 10 * 1024 * 1024; // 10MB limit (OpenAI API limit)
    const estimatedTokens = Math.ceil(contentLength / 4); // Rough estimate: 4 chars per token
    const maxTokens = 25000; // Token-based fallback limit

    console.log(
      `📊 Content size check: ${contentLength} characters (${(contentLength / (1024 * 1024)).toFixed(2)}MB) → ${estimatedTokens} estimated tokens`
    );
    console.log(
      `📏 Limits: Content size max ${(maxContentSize / (1024 * 1024)).toFixed(0)}MB, Token max ${maxTokens}`
    );

    // Initialize result variables for the new English-only approach
    let optimizedGermanTitle = cleanSourceTitle(data.title);
    let englishEnhancementResult: any = { success: false };
    let enhancementQuality: 'excellent' | 'good' | 'fair' | undefined;

    // Skip enhancement if requested by feed settings
    if (shouldSkipEnhancement) {
      console.log(
        '⏭️ Skipping all AI enhancement due to feed processing options'
      );
      optimizedGermanTitle = cleanSourceTitle(data.title);
      enhancementQuality = 'fair'; // Unenhanced content is fair quality
    } else if (shouldSkipTranslation && !isEnglishFeed) {
      console.log(
        '⏭️ Skipping translation due to feed settings - keeping German content'
      );
      optimizedGermanTitle = cleanSourceTitle(data.title);
      enhancementQuality = 'fair'; // Untranslated content is fair quality
    } else if (isEnglishFeed) {
      console.log('🏴󠁧󠁢󠁥󠁮󠁧󠁿 Processing English feed - using optimized processing...');

      try {
        // For English feeds, use faster, simpler enhancement with reduced token usage
        englishEnhancementResult = await englishOnlyContentEnhancement(
          data.title,
          data.content,
          [], // No key points for basic processing
          {
            temperature: 0.3, // Lower temperature for faster processing
            timeout: 15000, // Shorter timeout for English content
            includeProcessingMetadata: true,
          }
        );

        if (englishEnhancementResult.success && englishEnhancementResult.data) {
          console.log('✅ Optimized English processing completed');
          enhancementQuality = 'good'; // English content is already good quality
        } else {
          console.warn(
            '⚠️ Optimized processing failed for English feed, proceeding with basic processing'
          );
          enhancementQuality = 'fair';
        }
      } catch (error) {
        console.warn('⚠️ Optimized processing failed for English feed:', error);
        enhancementQuality = 'fair';
      }
    } else if (contentLength > maxContentSize || estimatedTokens > maxTokens) {
      const reason =
        contentLength > maxContentSize
          ? `content size ${(contentLength / (1024 * 1024)).toFixed(2)}MB exceeds ${(maxContentSize / (1024 * 1024)).toFixed(0)}MB limit`
          : `estimated ${estimatedTokens} tokens exceeds ${maxTokens} token limit`;

      console.warn(`⚠️ Content too large for English enhancement: ${reason}`);
      console.log('🔄 Content will be processed without enhancement...');

      optimizedGermanTitle = cleanSourceTitle(data.title);
      enhancementQuality = 'fair'; // Mark as fair quality due to size limitation
    } else {
      try {
        console.log('🚀 Using ENGLISH-ONLY ENHANCEMENT SYSTEM...');

        // Enhanced retry logic for better reliability
        let enhancementAttempts = 0;
        const maxAttempts = 3;
        let lastError: string | undefined;

        while (enhancementAttempts < maxAttempts) {
          enhancementAttempts++;
          console.log(
            `🔄 Enhancement attempt ${enhancementAttempts}/${maxAttempts}...`
          );

          try {
            // Use new English-only enhancement system with progressive timeout
            const timeoutMs = 30000 + (enhancementAttempts - 1) * 15000; // 30s, 45s, 60s
            englishEnhancementResult = await englishOnlyContentEnhancement(
              data.title,
              data.content,
              [], // No key points for basic processing
              {
                temperature: 0.7,
                timeout: timeoutMs,
                includeProcessingMetadata: true,
              }
            );

            // Detailed validation of enhancement result
            if (englishEnhancementResult.success) {
              if (englishEnhancementResult.data?.enhancedContent?.content) {
                const result = englishEnhancementResult.data;

                // Extract enhanced English content
                optimizedGermanTitle = cleanSourceTitle(data.title); // Keep original German title for reference
                enhancementQuality =
                  result.quality?.enhancementQuality || 'good';

                console.log(
                  '✅ English-only enhancement completed successfully'
                );
                console.log(
                  `   - Attempt: ${enhancementAttempts}/${maxAttempts}`
                );
                console.log(
                  `   - Enhanced English title: ${result.enhancedContent.title.substring(0, 50)}...`
                );
                console.log(
                  `   - Content quality score: ${result.quality?.contentScore || 'N/A'}`
                );
                console.log(`   - Enhancement quality: ${enhancementQuality}`);
                console.log(
                  `   - Processing time: ${englishEnhancementResult.metrics?.processingTime || 0}ms`
                );
                console.log(
                  `   - Insights generated: ${result.enhancedContent.keyInsights?.length || 0}`
                );
                console.log(
                  `   - Content length: ${result.enhancedContent.content.length} characters`
                );
                break; // Success - exit retry loop
              } else {
                lastError =
                  'Enhancement succeeded but no enhanced content returned';
                console.warn(`⚠️ Attempt ${enhancementAttempts}: ${lastError}`);
              }
            } else {
              lastError =
                englishEnhancementResult.error ||
                'Enhancement failed without specific error';
              console.warn(`⚠️ Attempt ${enhancementAttempts}: ${lastError}`);
            }

            // If this wasn't the last attempt, wait before retrying
            if (enhancementAttempts < maxAttempts) {
              const waitTime = enhancementAttempts * 2000; // 2s, 4s wait between retries
              console.log(`⏳ Waiting ${waitTime}ms before retry...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
            }
          } catch (attemptError) {
            lastError =
              attemptError instanceof Error
                ? attemptError.message
                : String(attemptError);
            console.warn(
              `⚠️ Attempt ${enhancementAttempts} failed:`,
              lastError
            );

            // If this wasn't the last attempt, wait before retrying
            if (enhancementAttempts < maxAttempts) {
              const waitTime = enhancementAttempts * 2000; // 2s, 4s wait between retries
              console.log(`⏳ Waiting ${waitTime}ms before retry...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
            }
          }
        }

        // If all attempts failed, throw the last error
        if (
          !englishEnhancementResult.success ||
          !englishEnhancementResult.data?.enhancedContent?.content
        ) {
          throw new Error(
            `All ${maxAttempts} enhancement attempts failed. Last error: ${lastError || 'Unknown error'}`
          );
        }
      } catch (error) {
        console.warn(
          '⚠️ English-only enhancement failed, will create article without enhancement:',
          error
        );

        optimizedGermanTitle = cleanSourceTitle(data.title);
        enhancementQuality = 'fair';
      }
    }

    // Step 2: Process original German content for source reference
    console.log('🔄 Converting original German content to Lexical format...');
    let originalContentFormattedLexical: object;
    try {
      const germanProcessor = ContentProcessorIntegration.getInstance();
      const germanResult = await germanProcessor.processContent(
        data.content,
        'html'
      );

      if (germanResult.metrics.success) {
        originalContentFormattedLexical = germanResult.lexicalContent;
        console.log(`✅ German content converted to Lexical successfully`);
      } else {
        console.warn(`⚠️ German content conversion failed, using empty state`);
        originalContentFormattedLexical = createEmptyLexicalState();
      }
    } catch (error) {
      console.error(`❌ German content conversion error:`, error);
      originalContentFormattedLexical = createEmptyLexicalState();
    }

    // Step 3: Process enhanced English content for English tab
    let enhancedEnglishContentLexical: object;
    if (
      englishEnhancementResult.success &&
      englishEnhancementResult.data?.enhancedContent?.content
    ) {
      console.log(
        '🔄 Converting enhanced English content to Lexical format...'
      );
      try {
        const enhancedContent =
          englishEnhancementResult.data.enhancedContent.content;
        const englishProcessor = ContentProcessorIntegration.getInstance();
        const sourceFormat = enhancedContent.includes('<')
          ? 'html'
          : 'markdown';

        const englishResult = await englishProcessor.processContent(
          enhancedContent,
          sourceFormat
        );

        if (englishResult.metrics.success) {
          enhancedEnglishContentLexical = englishResult.lexicalContent;
          console.log(
            '✅ Enhanced English content converted to Lexical successfully'
          );
        } else {
          console.warn(
            '⚠️ Enhanced English content conversion failed, using markdown fallback'
          );
          enhancedEnglishContentLexical =
            await markdownToLexical(enhancedContent);
        }
      } catch (error) {
        console.warn('⚠️ Failed to process enhanced English content:', error);
        enhancedEnglishContentLexical = createEmptyLexicalState();
      }
    } else {
      console.log('🔄 No enhanced content available, creating placeholder...');
      const placeholderContent = `# Content Enhancement Required

**Status**: Content processing pending

**Next Steps**:
- Article requires English enhancement processing
- Original German content available in Source Reference tab

**Note**: This article was created successfully but requires enhancement to complete processing.`;

      enhancedEnglishContentLexical =
        await markdownToLexical(placeholderContent);
    }

    // Step 4: Find which predefined keywords are present in the article content
    console.log(`🔍 Searching for predefined keywords in article content...`);
    const matchedKeywordIds = await findMatchedKeywordsInContent(
      data.title,
      data.content
    );

    // Debug: Inspect data before PayloadCMS creation
    console.log(`🔍 Pre-creation data inspection:`);
    console.log(`   - Original German title: "${optimizedGermanTitle}"`);
    console.log(`   - Enhancement quality: ${enhancementQuality}`);
    console.log(
      `   - Enhanced content available: ${englishEnhancementResult.success ? 'Yes' : 'No'}`
    );
    console.log(`   - Keywords matched: ${matchedKeywordIds.length}`);

    // Prepare English tab data and extract SEO keywords
    const englishTabData: any = {};
    let seoKeywords: { keyword: string }[] = [];
    let relatedCompanies: any[] = [];

    if (isEnglishFeed) {
      // For English feeds, use original content but still extract companies if available
      console.log(
        '📝 Preparing English content data (using original content)...'
      );

      englishTabData.enhancedTitle = cleanTitle(data.title);
      englishTabData.enhancedContent = enhancedEnglishContentLexical;
      englishTabData.enhancedSummary = `English content: ${data.title.substring(0, 100)}...`;

      // Create basic insights for English content
      englishTabData.enhancedKeyInsights = [
        { insight: 'Original English content from BBC' },
        { insight: 'Content processed without translation' },
        { insight: 'Company extraction applied where possible' },
      ];

      // Extract companies if OpenAI processing was successful
      if (englishEnhancementResult.success && englishEnhancementResult.data) {
        const enhancedData = englishEnhancementResult.data;
        const extractedCompanies =
          enhancedData.enhancedContent.relatedCompanies || [];
        relatedCompanies = extractedCompanies;

        // Extract SEO keywords if available
        const keywordsArray = enhancedData.enhancedContent.keywords || [];
        seoKeywords = keywordsArray.map((keyword: string) => ({ keyword }));

        // Add company names to SEO keywords for discoverability
        const companyKeywords = extractedCompanies.map((company: any) => ({
          keyword: company.name,
        }));
        seoKeywords = [...seoKeywords, ...companyKeywords];

        console.log(
          `🏢 Extracted ${extractedCompanies.length} companies from English content`
        );
        if (extractedCompanies.length > 0) {
          extractedCompanies.forEach((company: any, index: number) => {
            console.log(
              `   ${index + 1}. ${company.name}${company.tickerSymbol ? ` (${company.tickerSymbol})` : ''} - ${company.relevance} relevance, ${company.confidence}% confidence`
            );
          });
        }
      } else {
        // Fallback keywords for English content
        seoKeywords = [
          { keyword: 'English content' },
          { keyword: 'BBC' },
          { keyword: 'Financial news' },
        ];
      }

      // Set quality metrics for English content
      englishTabData.qualityScore = 90; // English content is high quality by default
      englishTabData.relevanceScore = 80;
      englishTabData.enhancementQuality = 'good';
    } else if (
      englishEnhancementResult.success &&
      englishEnhancementResult.data
    ) {
      // For German content with successful enhancement
      const enhancedData = englishEnhancementResult.data;

      englishTabData.enhancedTitle = enhancedData.enhancedContent.title;
      englishTabData.enhancedContent = enhancedEnglishContentLexical;
      englishTabData.enhancedSummary = enhancedData.enhancedContent.summary;

      // Convert string arrays to object arrays for PayloadCMS
      englishTabData.enhancedKeyInsights = (
        enhancedData.enhancedContent.keyInsights || []
      ).map((insight: string) => ({ insight }));

      // Extract SEO keywords for both English tab and sidebar
      const keywordsArray = enhancedData.enhancedContent.keywords || [];
      seoKeywords = keywordsArray.map((keyword: string) => ({ keyword }));

      // Extract related companies and add company names to keywords
      const extractedCompanies =
        enhancedData.enhancedContent.relatedCompanies || [];
      relatedCompanies = extractedCompanies;

      // Add company names to SEO keywords for discoverability
      const companyKeywords = extractedCompanies.map((company: any) => ({
        keyword: company.name,
      }));
      seoKeywords = [...seoKeywords, ...companyKeywords];

      console.log(
        `🏢 Extracted ${extractedCompanies.length} companies from content`
      );
      if (extractedCompanies.length > 0) {
        extractedCompanies.forEach((company: any, index: number) => {
          console.log(
            `   ${index + 1}. ${company.name}${company.tickerSymbol ? ` (${company.tickerSymbol})` : ''} - ${company.relevance} relevance, ${company.confidence}% confidence`
          );
        });
      }

      // Extract quality metrics with proper field mapping
      englishTabData.qualityScore = enhancedData.quality?.contentScore || 85;
      englishTabData.relevanceScore =
        enhancedData.quality?.relevanceScore || 75;
      englishTabData.enhancementQuality =
        enhancedData.quality?.enhancementQuality || enhancementQuality;
    } else {
      // Fallback case: Neither English feed nor successful enhancement
      // This covers scenarios like:
      // - skipEnhancement = true
      // - skipTranslation = true (for German feeds)
      // - Content too large for processing
      // - OpenAI enhancement failed
      console.log(
        '📝 Preparing fallback content data (enhancement skipped or failed)...'
      );

      englishTabData.enhancedTitle = cleanTitle(data.title);
      englishTabData.enhancedContent = enhancedEnglishContentLexical;
      englishTabData.enhancedSummary = `Content processing completed: ${data.title.substring(0, 100)}...`;

      // Create basic insights for fallback content
      englishTabData.enhancedKeyInsights = [
        { insight: 'Content processed without AI enhancement' },
        { insight: 'Original content available in Source Reference tab' },
        { insight: 'Enhancement may be available in future processing' },
      ];

      // Fallback keywords
      seoKeywords = [
        { keyword: 'Financial news' },
        { keyword: 'Market analysis' },
        { keyword: 'Investment' },
      ];

      // Set quality metrics for fallback content
      englishTabData.qualityScore = 70; // Reasonable quality without enhancement
      englishTabData.relevanceScore = 65;
      englishTabData.enhancementQuality = enhancementQuality || 'fair';
    }

    // Prepare Source Reference tab data
    const sourceReferenceTabData = {
      sourceFeed: Number.parseInt(data.sourceFeed), // RSS feed that provided this article
      originalTitle: optimizedGermanTitle,
      originalContent: originalContentFormattedLexical,
      originalPublishedAt: data.publishedDate?.toISOString(),
      sourceUrl: data.sourceUrl,
      // Removed processingMetadata field as it doesn't exist in the PayloadCMS schema
      // This was preventing the entire sourcesTab from saving properly
    };

    // Add validation logging for publication date
    if (data.publishedDate) {
      console.log(
        `📅 Article will include source publication date: ${data.publishedDate.toISOString()}`
      );
    } else {
      console.warn(
        `⚠️ Article created without source publication date for: ${data.title}`
      );
      console.warn('   - Check Firecrawl extraction and German date parsing');
      console.warn(
        '   - Source Published Date will be blank in admin interface'
      );
    }

    // Create the article with new structure
    const result = await payload.create({
      collection: 'articles',
      data: {
        articleType: 'generated',
        title: englishTabData.enhancedTitle || tempTitle, // Use enhanced title if available
        // slug: Don't set slug - let the hooks generate it from the enhanced title
        workflowStage: 'candidate-article',
        // Generated articles are already AI-enhanced during import, so mark as enhanced
        hasBeenEnhanced: true,

        // Related Companies extracted by AI (sidebar field) - map field names
        relatedCompanies: relatedCompanies.map((company: any) => ({
          name: company.name,
          ticker: company.tickerSymbol, // Map tickerSymbol to ticker
          exchange: company.exchange, // Map exchange symbol
          relevance: company.relevance,
          confidence: company.confidence,
        })),

        // Keywords matched during RSS processing
        keywordsMatched: matchedKeywordIds,

        // New English tab - primary enhanced content
        englishTab:
          Object.keys(englishTabData).length > 0
            ? {
                ...englishTabData,
                keywords: seoKeywords, // Move keywords to English tab
              }
            : undefined,

        // New Sources tab - original German content for reference
        sourcesTab: sourceReferenceTabData,
      } as any, // Cast to any to handle new fields during transition
    });

    console.log(
      `✅ Created candidate article with English-only enhancement and ${matchedKeywordIds.length} predefined keywords found: ${data.title} (ID: ${result.id})`
    );
    console.log(
      `📊 Article structure: ${englishTabData.enhancedTitle ? 'Enhanced English content' : 'Pending enhancement'} + German reference`
    );

    return { id: result.id.toString() };
  } catch (error: any) {
    console.error('❌ Failed to create candidate article:', error);
    throw new Error(`Failed to create candidate article: ${error.message}`);
  }
}

/**
 * Find which predefined keywords from the Keywords collection are present in the article content
 * This shows which filtering keywords triggered the article's acceptance
 */
async function findMatchedKeywordsInContent(
  title: string,
  content: string
): Promise<number[]> {
  try {
    const payload = await getPayload({ config });

    // Get all active keywords from database
    const allKeywords = await payload.find({
      collection: 'keywords',
      where: {
        isActive: { equals: true },
      },
      limit: 1000,
    });

    const matchedIds: number[] = [];
    const keywordsToUpdate: number[] = [];

    // Combine title and content for searching
    const fullText = `${title} ${content}`.toLowerCase();

    // Check each predefined keyword against the article content
    for (const dbKeyword of allKeywords.docs) {
      const germanKeyword = dbKeyword.keyword.toLowerCase();
      const englishKeyword = dbKeyword.englishKeyword.toLowerCase();

      // Check if either German or English version appears in the content
      const germanFound = fullText.includes(germanKeyword);
      const englishFound = fullText.includes(englishKeyword);

      if (germanFound || englishFound) {
        const keywordId =
          typeof dbKeyword.id === 'string'
            ? parseInt(dbKeyword.id)
            : dbKeyword.id;
        matchedIds.push(keywordId);
        keywordsToUpdate.push(keywordId);
        const foundVersion = germanFound ? germanKeyword : englishKeyword;
        console.log(
          `✅ Found predefined keyword in content: "${foundVersion}" → ${dbKeyword.keyword} (${dbKeyword.englishKeyword})`
        );
      }
    }

    // Update usage counts for matched keywords
    for (const keywordId of keywordsToUpdate) {
      try {
        const keyword = await payload.findByID({
          collection: 'keywords',
          id: keywordId,
        });

        await payload.update({
          collection: 'keywords',
          id: keywordId,
          data: {
            usageCount: (keyword.usageCount || 0) + 1,
          },
        });
      } catch (error: any) {
        console.error(
          `❌ Failed to update usage count for keyword ${keywordId}:`,
          error.message
        );
      }
    }

    console.log(
      `🔗 Found ${matchedIds.length} predefined keywords in article content`
    );
    return matchedIds;
  } catch (error: any) {
    console.error('❌ Failed to find keywords in content:', error.message);
    return [];
  }
}

/**
 * Get candidate articles for review
 */
export async function getCandidateArticles(
  limit = 20,
  page = 1
): Promise<{
  docs: any[];
  totalDocs: number;
  totalPages: number;
  page: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}> {
  try {
    const payload = await getPayload({ config });

    const result = await payload.find({
      collection: 'articles',
      where: {
        workflowStage: {
          equals: 'candidate-article',
        },
      },
      limit,
      page,
      sort: '-createdAt',
    });

    return {
      ...result,
      page: result.page || 1,
    };
  } catch (error: any) {
    console.error('❌ Failed to get candidate articles:', error);
    throw new Error(`Failed to get candidate articles: ${error.message}`);
  }
}

/**
 * Update article workflow stage (editorial stages only)
 * Publication is handled via PayloadCMS native draft/publish system
 */
export async function updateArticleWorkflowStage(
  id: string,
  workflowStage: 'candidate-article' | 'translated' | 'ready-for-review',
  userId?: string
): Promise<void> {
  try {
    const payload = await getPayload({ config });

    const updateData: any = {
      workflowStage,
    };

    // Add workflow tracking for editorial stages only
    // (Publication is handled by PayloadCMS native draft/publish system)
    if (workflowStage === 'ready-for-review' && userId) {
      updateData.reviewedBy = userId;
      updateData.reviewedAt = new Date();
    }

    await payload.update({
      collection: 'articles',
      id,
      data: updateData,
    });

    console.log(`✅ Updated article workflow stage: ${id} -> ${workflowStage}`);
  } catch (error: any) {
    console.error('❌ Failed to update article workflow stage:', error);
    throw new Error(
      `Failed to update article workflow stage: ${error.message}`
    );
  }
}

/**
 * Get article by ID
 */
export async function getArticleById(id: string): Promise<any> {
  try {
    const payload = await getPayload({ config });

    const result = await payload.findByID({
      collection: 'articles',
      id,
    });

    return result;
  } catch (error: any) {
    console.error('❌ Failed to get article by ID:', error);
    throw new Error(`Failed to get article by ID: ${error.message}`);
  }
}

/**
 * Search articles with filters
 */
export async function searchArticles(params: {
  query?: string;
  workflowStage?: string[];
  sourceFeed?: string[];
  limit?: number;
  page?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}): Promise<{
  docs: any[];
  totalDocs: number;
  totalPages: number;
  page: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}> {
  try {
    const payload = await getPayload({ config });

    const where: any = {};

    // Build query conditions
    if (params.workflowStage && params.workflowStage.length > 0) {
      where.workflowStage = {
        in: params.workflowStage,
      };
    }

    if (params.sourceFeed && params.sourceFeed.length > 0) {
      where.sourceFeed = {
        in: params.sourceFeed,
      };
    }

    // Text search
    if (params.query) {
      where.or = [
        {
          title: {
            contains: params.query,
          },
        },
        {
          originalContent: {
            contains: params.query,
          },
        },
      ];
    }

    // Sort configuration
    let sort = '-createdAt'; // Default sort
    if (params.sortBy) {
      const direction = params.sortOrder === 'asc' ? '' : '-';
      sort = `${direction}${params.sortBy}`;
    }

    const result = await payload.find({
      collection: 'articles',
      where,
      limit: params.limit || 20,
      page: params.page || 1,
      sort,
    });

    return {
      ...result,
      page: result.page || 1,
    };
  } catch (error: any) {
    console.error('❌ Failed to search articles:', error);
    throw new Error(`Failed to search articles: ${error.message}`);
  }
}
