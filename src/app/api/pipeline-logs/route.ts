import { NextRequest, NextResponse } from 'next/server';
import { readdir, readFile } from 'fs/promises';
import { join } from 'path';
import { withAuth } from '@/lib/auth/simple-auth';

/**
 * Pipeline Logs API
 * View and manage content pipeline error logs
 * 
 * GET /api/pipeline-logs - List recent error logs
 * GET /api/pipeline-logs?component=content-pipeline - Filter by component
 * GET /api/pipeline-logs?date=2025-01-24 - Filter by date
 */

async function _GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const component = searchParams.get('component') || 'content-pipeline';
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0];
    const limit = parseInt(searchParams.get('limit') || '50');

    const logsDir = join(process.cwd(), 'logs');
    const logFileName = `${component}-${date}.log`;
    const logFilePath = join(logsDir, logFileName);

    let logs: any[] = [];
    let availableFiles: string[] = [];

    try {
      // Get list of available log files
      const files = await readdir(logsDir);
      availableFiles = files
        .filter(file => file.endsWith('.log'))
        .sort()
        .reverse(); // Most recent first

      // Read the specific log file
      const logContent = await readFile(logFilePath, 'utf-8');
      const logLines = logContent.trim().split('\n').filter(line => line);
      
      // Parse JSON log entries
      logs = logLines
        .map(line => {
          try {
            return JSON.parse(line);
          } catch {
            return null;
          }
        })
        .filter(Boolean)
        .slice(-limit); // Get most recent entries

    } catch (error) {
      // Log file doesn't exist or can't be read
      console.log(`Log file not found: ${logFilePath}`);
    }

    return NextResponse.json({
      success: true,
      data: {
        logs,
        totalEntries: logs.length,
        component,
        date,
        logFile: logFileName,
        availableFiles,
      },
      meta: {
        requestedLimit: limit,
        actualCount: logs.length,
        hasMoreFiles: availableFiles.length > 1,
      },
    });

  } catch (error: any) {
    console.error('Failed to read pipeline logs:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to read pipeline logs',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

/**
 * Clear pipeline logs
 * DELETE /api/pipeline-logs?component=content-pipeline&date=2025-01-24
 */
async function _DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const component = searchParams.get('component') || 'content-pipeline';
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0];

    const logsDir = join(process.cwd(), 'logs');
    const logFileName = `${component}-${date}.log`;
    const logFilePath = join(logsDir, logFileName);

    // Write empty content to clear the log
    const { writeFile } = await import('fs/promises');
    await writeFile(logFilePath, '');

    return NextResponse.json({
      success: true,
      message: `Pipeline log cleared: ${logFileName}`,
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('Failed to clear pipeline logs:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to clear pipeline logs',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// Apply authentication
export const GET = withAuth(_GET);
export const DELETE = withAuth(_DELETE);
